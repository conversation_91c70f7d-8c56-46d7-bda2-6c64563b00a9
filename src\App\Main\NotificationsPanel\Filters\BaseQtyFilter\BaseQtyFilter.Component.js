import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { propOr, isEmpty } from 'ramda';
import FormattedNumberField from 'lib/components/FormattedNumberField';
import { blockInvalidChar } from 'lib/utils';
import Field from 'lib/components/Fields/Field';
import { getDisabledFilterTooltip } from 'lib/components/GenericTable/Filters/filterUtils';
import { Wrapper, RangeRow } from './BaseQtyFilter.Styles';

const BaseQtyFilter = ({
  disabled, disabledTooltip, value = {}, setValue, t,
}) => {
  const baseMin = propOr('', 'baseQuantityMin', value);
  const baseMax = propOr('', 'baseQuantityMax', value);

  const updateValue = (newMin, newMax) => {
    setValue(isEmpty(newMin) && isEmpty(newMax)
      ? undefined
      : { baseQuantityMin: newMin, baseQuantityMax: newMax });
  };

  return (
    <Wrapper title={getDisabledFilterTooltip(disabled, disabledTooltip)}>
      <Field title={t('order.snapshot.baseQuantity')}>
        <RangeRow>
          <FormattedNumberField
            variant="outlined"
            placeholder={t('notification.qtyRange.from')}
            value={baseMin}
            disabled={disabled}
            onValueChange={(from) => updateValue(from, baseMax)}
            inputProps={{ min: 0 }}
            onKeyDown={blockInvalidChar}
          />
          <FormattedNumberField
            variant="outlined"
            placeholder={t('notification.qtyRange.to')}
            value={baseMax}
            disabled={disabled}
            onValueChange={(to) => updateValue(baseMin, to)}
            inputProps={{ min: 0 }}
            onKeyDown={blockInvalidChar}
          />
        </RangeRow>
      </Field>
    </Wrapper>
  );
};

BaseQtyFilter.propTypes = {
  setValue: PropTypes.func.isRequired,
  t: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
  value: PropTypes.shape({
    baseQuantityMin: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    baseQuantityMax: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  }),
  disabledTooltip: PropTypes.string,
};

BaseQtyFilter.defaultProps = {
  disabled: false,
  value: {},
  disabledTooltip: '',
};

export default withTranslation()(BaseQtyFilter);
