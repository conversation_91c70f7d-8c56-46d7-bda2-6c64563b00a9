import styled from 'styled-components';
import { fonts } from 'lib/theme/theme';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';

export const Wrapper = styled.div`
  display: flex;
  justify-content: space-between;
  padding: 1rem 0;
`;

export const Icons = styled.div`
  display: flex;
`;

export const Icon = styled.div`
  cursor: pointer;
  padding: 0 0.5rem;
`;

export const Title = styled.div`
  font-size: ${fonts.mediumTitle.size};
  line-height: ${fonts.mediumTitle.line};
  margin-right: 1rem;
  margin-left: 1rem;
`;

export const Checkmark = styled(CheckCircleIcon)`
  position: absolute;
  top: 0;
  right: 3px;
  color: #4caf50;
  font-size: 12px;
  background-color: white;
  border-radius: 50%;
`;
