import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  getPositionToBookNow,
  confirmBookNow,
  cancelBookNowConfirmationDialog,
} from 'modules/bookingAggregation';
import Component from './BookNowConfirmationDialog.Component';

const mapStateToProps = createStructuredSelector({
  position: getPositionToBookNow,
});

const mapDispatchToProps = {
  cancel: cancelBookNowConfirmationDialog,
  confirm: confirmBookNow,
};

export default connect(mapStateToProps, mapDispatchToProps)(Component);
