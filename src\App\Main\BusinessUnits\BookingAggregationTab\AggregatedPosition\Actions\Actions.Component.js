import PropTypes from 'prop-types';
import RocketLaunchOutlinedIcon from '@mui/icons-material/RocketLaunchOutlined';
import ListAltOutlinedIcon from '@mui/icons-material/ListAltOutlined';
import ActionBtn from 'lib/components/GenericTable/Table/Cell/EditButton';
import { ActionGroup } from './Actions.Styles';

const Actions = ({
  row, bookPosition, openPositionDetail,
  canBookPosition,
}) => (
  <ActionGroup>
    <ActionBtn
      id="aggregationPosition.book"
      icon={<RocketLaunchOutlinedIcon sx={{ fontSize: 20 }} />}
      onClick={bookPosition}
      row={row}
      isVisible={canBookPosition}
    />
    <ActionBtn
      id="aggregationPosition.detail"
      icon={<ListAltOutlinedIcon sx={{ fontSize: 20 }} />}
      onClick={openPositionDetail}
      row={row}
    />
  </ActionGroup>
);

Actions.propTypes = {
  bookPosition: PropTypes.func.isRequired,
  openPositionDetail: PropTypes.func.isRequired,
  row: PropTypes.object.isRequired,
  canBookPosition: PropTypes.bool.isRequired,
};

export default Actions;
