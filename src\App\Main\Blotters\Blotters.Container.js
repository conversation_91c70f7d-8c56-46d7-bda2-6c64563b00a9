import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { compose, equals } from 'ramda';
import { MENU_OPTION } from 'lib/constants';
import { setDetached, isLoading } from 'modules/blotter';
import { isDetached, getCurrentPage } from 'modules/main';

import Component from './Blotters.Component';

const mapStateToProps = createStructuredSelector({
  isCurrentPage: compose(equals(MENU_OPTION.BLOTTERS), getCurrentPage),
  detached: isDetached(MENU_OPTION.BLOTTERS),
  loading: isLoading,
});

const mapDispatchToProps = ({
  setDetached,
});

export default connect(mapStateToProps, mapDispatchToProps)(Component);
