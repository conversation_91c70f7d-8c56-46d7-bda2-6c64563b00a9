import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  getBlotterSelectedTab,
  setBlotterSelectedTab,
  getBlotterColumnFilters,
} from 'modules/settings';
import Component from 'lib/components/GenericTable/OrdersTable/TabsSelector';

const mapStateToProps = createStructuredSelector({
  selectedTab: getBlotterSelectedTab,
  filters: getBlotterColumnFilters,
});

const mapDispatchToProps = {
  setSelectedTab: setBlotterSelectedTab,
};

export default connect(mapStateToProps, mapDispatchToProps)(Component);
