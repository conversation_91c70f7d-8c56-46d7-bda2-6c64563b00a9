import styled from 'styled-components';
import { colors, fonts } from 'lib/theme/theme';

export const Wrapper = styled.div`
  display: flex;
  height: 2.6875rem;
  min-height: 2.6875rem;
  align-items: center;
  justify-items: start;
  justify-content: space-between;
  font-size: ${fonts.smallText.size};
  lineHeight: ${fonts.smallText.line};
  font-weight: bold;
  border-top: 1px solid ${colors.borderColor};
  position: absolute;
  bottom: 0;
  width: 100%;
  overflow: scroll;
  flex-wrap: wrap;
  background-color: ${colors.backgroundColor};
`;

export const PositionWrapper = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-left: 0.5rem;
`;

export const Cell = styled.div`
  display: flex;
  padding-left: 1.3rem;
  align-items: center;
`;

export const Text = styled.span`
  display: flex;
  padding-right: 0.25rem;
  color: ${(props) => colors[props.$color]};
`;

export const Title = styled.span`
  display: flex;
  font-weight: normal;
  padding-right: 0.25rem;
  color: ${(props) => colors[props.$color]};
`;

export const Dot = styled.div`
  display: block;
  width: 0.5625rem;
  height: 0.5625rem;
  min-width: 0.5625rem;
  min-height: 0.5625rem;
  border-radius: 0.56rem;
  margin-right: 0.25rem;
  background-color: ${(props) => colors.metals[props.$color]};
`;
