import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  getBlotterTableFilters, removeBlotterSingleFilterValue, getBlotterBaseQtyFilterRangeType,
} from 'modules/settings';
import { getCurrencyPairsDisplayNames } from 'modules/user';
import Component from 'lib/components/GenericTable/OrdersTable/CurrentFilters';

const mapStateToProps = createStructuredSelector({
  filters: getBlotterTableFilters,
  ccyPairDisplayNames: getCurrencyPairsDisplayNames,
  baseQtyRangeType: getBlotterBaseQtyFilterRangeType,
});

const mapDispatchToProps = ({
  removeSingleFilterValue: removeBlotterSingleFilterValue,
});

export default connect(mapStateToProps, mapDispatchToProps)(Component);
