import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import FilterListIcon from '@mui/icons-material/FilterList';
import DownloadIcon from '@mui/icons-material/Download';
import IconButton from '@mui/material/IconButton';
import GlobalFilter from 'lib/components/GenericTable/GlobalFilter';
import DetachButton from 'lib/components/DetachButton';
import AddColumns from './AddColumns';
import { Wrapper, Item } from './Actions.Styles';

const Actions = ({
  detached,
  setDetached,
  openFilterPane,
  setGlobalFilter,
  exportToCSV,
  canExportToCSV,
  t,
}) => (
  <Wrapper>
    <GlobalFilter
      setGlobalFilter={setGlobalFilter}
    />
    <Item>
      <AddColumns />
    </Item>
    <Item>
      <IconButton
        id="action.blotter.download"
        title={t('generic.action.download')}
        disabled={!canExportToCSV}
        onClick={exportToCSV}
      >
        <DownloadIcon />
      </IconButton>
    </Item>
    <Item>
      <IconButton
        id="filters.access"
        title={t('generic.action.filters')}
        onClick={openFilterPane}
      >
        <FilterListIcon />
      </IconButton>
    </Item>
    <DetachButton
      detached={detached}
      setDetached={setDetached}
    />
  </Wrapper>
);

Actions.propTypes = {
  detached: PropTypes.bool.isRequired,
  setDetached: PropTypes.func.isRequired,
  openFilterPane: PropTypes.func.isRequired,
  setGlobalFilter: PropTypes.func.isRequired,
  exportToCSV: PropTypes.func.isRequired,
  canExportToCSV: PropTypes.bool.isRequired,
  t: PropTypes.func.isRequired,
};

export default withTranslation()(Actions);
