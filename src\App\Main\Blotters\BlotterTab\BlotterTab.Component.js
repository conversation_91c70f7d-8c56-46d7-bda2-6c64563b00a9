import { useEffect, useMemo, useState } from 'react';
import { withTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import {
  equals,
  isEmpty,
  toLower,
  map,
  compose,
  __,
  pluck,
} from 'ramda';
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getFilteredRowModel,
} from '@tanstack/react-table';
import { generateCsvFile } from 'lib/csvutils';
import {
  columnLabels,
  ACTION_COLUMN,
} from 'lib/constants';
import { BlotterTabContext } from 'lib/hooks/useBlotterTab';
import {
  rejectEmptyOrNilValues,
  toReactTableColumns,
  prepareRealColumnSizes,
} from 'lib/components/GenericTable/tableUtils';
import Table from 'lib/components/GenericTable/Table';
import {
  getRenderCellFunction,
  getFilterFn,
  ROW_HEIGHT,
  DEFAULT_ORDER_COLUMN_WIDTH,
} from 'lib/components/GenericTable/OrdersTable/tableUtils';
import { calculatePositionSummary } from './Footer/footerUtils';
import { TableWrapper, Wrapper } from './BlotterTab.Styles';
import TopBar from './TopBar';
import Filters from './Filters';
import CurrentFilters from './CurrentFilters';
import Footer from './Footer';
import RebookConfirmationDialog from './RebookConfirmationDialog';

const BlotterTab = ({
  tabName,
  data,
  columnIds,
  isMasterDataLoaded,
  fetchOrders,
  columnWidths: customWidths,
  setColumnWidths,
  setPositionSummary,
  sortOrder,
  timeZone,
  filters,
  startDate,
  endDate,
  selectedTab,
  setSortOrder,
  setColumnFilters,
  removeColumn,
  moveColumn,
  scrollPosition,
  setScrollPosition,
  t,
}) => {
  const [globalFilter, setGlobalFilter] = useState([]);
  const [sorting, setSorting] = useState(sortOrder);

  useEffect(
    () => {
      (async () => fetchOrders(tabName))();
    },
    [timeZone, startDate, endDate, isMasterDataLoaded],
  );

  const prepareColumns = map((columnId) => ({
    id: columnId,
    accessor: columnId,
    label: columnLabels[columnId],
    size: columnId === ACTION_COLUMN ? 64 : customWidths[columnId] || DEFAULT_ORDER_COLUMN_WIDTH,
    customWidth: customWidths[columnId],
    renderType: getRenderCellFunction(columnId),
    enableResizing: columnId !== ACTION_COLUMN,
    isRemovable: columnId !== ACTION_COLUMN,
    draggable: columnId !== ACTION_COLUMN,
    fixedWidth: columnId === ACTION_COLUMN ? '4rem' : undefined,
    enableSorting: columnId !== ACTION_COLUMN,
    filterFn: getFilterFn(columnId),
  }));

  const reactTableColumns = useMemo(() => compose(
    toReactTableColumns(__, t),
    prepareColumns,
  )(columnIds), [columnIds, customWidths]);

  const tableData = useMemo(() => rejectEmptyOrNilValues(data), [data]);

  const table = useReactTable({
    data: tableData,
    columns: reactTableColumns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    globalFilterFn: 'includesString',
    state: {
      sorting,
      globalFilter,
      columnFilters: filters,
    },
    onColumnFiltersChange: setColumnFilters,
    columnResizeMode: 'onChange',
  });

  const { rows } = table.getRowModel();

  const columnSizeVars = useMemo(() => prepareRealColumnSizes(table),
    [
      table.getState().columnSizingInfo,
      table.getState().columnSizing,
      columnIds,
    ]);

  const setSortOnClick = (newSort) => {
    setSorting(newSort);
    setSortOrder(newSort);
  };

  useEffect(
    () => {
      const orders = isEmpty(rows) ? [] : pluck('original', rows);
      setPositionSummary(calculatePositionSummary(orders));
    },
    [rows],
  );

  const tableWidth = `${table.getTotalSize()}px`;

  return (equals(tabName, selectedTab)) ? (
    <BlotterTabContext.Provider value={tabName}>
      <Wrapper id={`blotter.${tabName}`}>
        <TopBar
          setGlobalFilter={setGlobalFilter}
          exportToCSV={() => generateCsvFile(toLower(tabName), columnIds, rows)}
          canExportToCSV={!isEmpty(data)}
        />

        <TableWrapper>
          <Wrapper id="table.wrapper">
            <Filters tabName={tabName} />
            <CurrentFilters tabName={tabName} />
            <Table
              table={table}
              headerGroups={table.getHeaderGroups()}
              itemData={{
                items: rows,
                rowHeight: ROW_HEIGHT,
                columnSizeVars,
                tableWidth,
              }}
              setSortOrder={setSortOnClick}
              setColumnWidths={setColumnWidths}
              removeColumn={(columnId) => removeColumn(columnId, tabName)}
              moveColumn={(source, target) => moveColumn(source, target, tabName)}
              tableWidth={tableWidth}
              initialScrollPosition={scrollPosition}
              setScrollPosition={(value) => setScrollPosition(value, selectedTab)}
              orderRowsType
            />
            <Footer rowCount={table.getFilteredRowModel().rows.length} />
          </Wrapper>
        </TableWrapper>
        <RebookConfirmationDialog />
      </Wrapper>
    </BlotterTabContext.Provider>
  ) : null;
};

BlotterTab.propTypes = {
  tabName: PropTypes.string.isRequired,
  data: PropTypes.arrayOf(PropTypes.object).isRequired,
  fetchOrders: PropTypes.func.isRequired,
  columnIds: PropTypes.arrayOf(PropTypes.string).isRequired,
  sortOrder: PropTypes.arrayOf(PropTypes.object).isRequired,
  selectedTab: PropTypes.string.isRequired,
  columnWidths: PropTypes.object.isRequired,
  setColumnWidths: PropTypes.func.isRequired,
  removeColumn: PropTypes.func.isRequired,
  moveColumn: PropTypes.func.isRequired,
  setColumnFilters: PropTypes.func.isRequired,
  setPositionSummary: PropTypes.func.isRequired,
  timeZone: PropTypes.string.isRequired,
  filters: PropTypes.arrayOf(PropTypes.object).isRequired,
  startDate: PropTypes.instanceOf(Date).isRequired,
  endDate: PropTypes.instanceOf(Date).isRequired,
  isMasterDataLoaded: PropTypes.bool.isRequired,
  setSortOrder: PropTypes.func.isRequired,
  scrollPosition: PropTypes.number.isRequired,
  setScrollPosition: PropTypes.func.isRequired,
  t: PropTypes.func.isRequired,
};

export default withTranslation()(BlotterTab);
