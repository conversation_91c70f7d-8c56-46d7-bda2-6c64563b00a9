import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import {
  values,
} from 'ramda';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import {
  COLUMN_ORDERTYPE,
  ORDER_TYPES,
} from 'lib/constants';
import HeaderTitleWithClose from 'lib/components/HeaderTitleWithClose/HeaderTitleWithClose.Component';
import ClearFilters from './ClearButton';
import CPSelector from './CPSelector';
import OrderTypeFilter from './OrderTypeFilter';
import BaseQtyFilter from './BaseQtyFilter';
import {
  Wrapper,
  StyledDrawer, BoxContainer,
} from './Filters.Styles';
import ActionResultFilterComponent from './ActionResultFilter';
import InternalizedFilter from './InternalizedFilter';

const Filters = ({
  opened, close, detached,
}) => {
  const { t } = useTranslation();
  return (
    <ClickAwayListener
      mouseEvent="onMouseDown"
      onClickAway={close}
    >
      <Wrapper>
        <StyledDrawer
          id="filters"
          anchor="right"
          open={opened}
          BackdropProps={{ invisible: true }}
          variant="persistent"
          $isDetached={detached}
        >
          <BoxContainer>
            <HeaderTitleWithClose title={t('table.filter.headertext')} onClick={close} />
          </BoxContainer>
          <BoxContainer id="filter.cp">
            <CPSelector
              multiple
              disablePortal={false}
            />
          </BoxContainer>
          <BoxContainer id="filter.baseQuantityRaw">
            <BaseQtyFilter />
          </BoxContainer>
          <InternalizedFilter />
          <ActionResultFilterComponent />
          <OrderTypeFilter
            filterName={COLUMN_ORDERTYPE}
            options={values(ORDER_TYPES)}
            disabled={false}
          />
          <ClearFilters />
        </StyledDrawer>
      </Wrapper>
    </ClickAwayListener>
  );
};

Filters.propTypes = {
  opened: PropTypes.bool.isRequired,
  close: PropTypes.func.isRequired,
  detached: PropTypes.bool.isRequired,
};

export default Filters;
