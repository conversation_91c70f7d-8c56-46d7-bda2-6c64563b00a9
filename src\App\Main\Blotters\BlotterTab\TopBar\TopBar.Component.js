import PropTypes from 'prop-types';
import { keys } from 'ramda';
import { BLOTTER_TABS } from 'lib/constants';
import Actions from './Actions';
import BlotterSelector from './BlotterSelector';
import { Wrapper } from './TopBar.Styles';

const TopBar = ({
  setGlobalFilter, exportToCSV, canExportToCSV,
}) => (
  <Wrapper>
    <BlotterSelector
      id="blotters.tabselector"
      tabsList={keys(BLOTTER_TABS)}
      detachable={false}
    />
    <Actions
      setGlobalFilter={setGlobalFilter}
      exportToCSV={exportToCSV}
      canExportToCSV={canExportToCSV}
    />
  </Wrapper>
);

TopBar.propTypes = {
  setGlobalFilter: PropTypes.func.isRequired,
  canExportToCSV: PropTypes.bool.isRequired,
  exportToCSV: PropTypes.func.isRequired,
};

export default TopBar;
