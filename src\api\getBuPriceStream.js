import axios from 'api/getAxios';
import { encodeCurrencyPair, debugLog } from 'lib/utils';

export default async (buId, userId, cpId, tenor, valueDate) => {
  const { data: requestId } = await axios.get(`/bu-price/${buId}/${userId}/${encodeCurrencyPair(cpId)}/${tenor}/${valueDate}`);
  debugLog('BU MDS request:', `${buId} - ${userId} - ${cpId} - ${tenor} - ${valueDate} - ${requestId}`);
  return requestId;
};
