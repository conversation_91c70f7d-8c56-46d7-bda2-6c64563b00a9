import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  getAuctionCommissions,
  getAllFormattedCommissions,
  getSortOrder,
  setSortOrder,
  getAuctionCommissionOverrides,
} from 'modules/auctionCommissions';
import Component from './DefaultCommission.Component';

const mapStateToProps = createStructuredSelector({
  commissions: getAllFormattedCommissions,
  sortOrder: getSortOrder,
});

const mapDispatchToProps = ({
  getAuctionCommissions,
  setSortOrder,
  getAuctionCommissionOverrides,
});

export default connect(mapStateToProps, mapDispatchToProps)(Component);
