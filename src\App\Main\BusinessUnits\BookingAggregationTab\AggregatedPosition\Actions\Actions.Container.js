import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { setPositionToBookNow, openPositionDetail } from 'modules/bookingAggregation';
import { canBookAggregatedPosition } from 'modules/user';
import Component from './Actions.Component';

const mapStateToProps = createStructuredSelector({
  canBookPosition: canBookAggregatedPosition,
});

const mapDispatchToProps = ({
  bookPosition: setPositionToBookNow,
  openPositionDetail,
});

export default connect(mapStateToProps, mapDispatchToProps)(Component);
