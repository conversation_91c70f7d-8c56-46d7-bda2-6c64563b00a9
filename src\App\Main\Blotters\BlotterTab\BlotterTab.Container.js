import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  getOrders,
  fetchOrders,
  setPositionSummary,
  getStartDateFilter,
  getEndDateFilter,
  getScrollPosition,
  setScrollPosition,
} from 'modules/blotter';
import {
  getBlotterColumns,
  updateBlotterColumnWidths,
  getBlotterColumnWidths,
  getBlotterSortOrder,
  getTimeZone,
  getBlotterColumnFilters,
  getBlotterSelectedTab,
  updateBlotterSortOrder,
  setBlotterColumnFilters,
  removeBlotterColumn,
  moveBlotterColumn,
} from 'modules/settings';
import { isMasterDataLoaded } from 'modules/masterData';
import Component from './BlotterTab.Component';

const mapStateToProps = createStructuredSelector({
  data: getOrders,
  columnIds: getBlotterColumns,
  columnWidths: getBlotterColumnWidths,
  sortOrder: getBlotterSortOrder,
  timeZone: getTimeZone,
  filters: getBlotterColumnFilters,
  isMasterDataLoaded,
  startDate: getStartDateFilter,
  endDate: getEndDateFilter,
  selectedTab: getBlotterSelectedTab,
  scrollPosition: getScrollPosition,
});

const mapDispatchToProps = ({
  fetchOrders,
  setColumnWidths: updateBlotterColumnWidths,
  setPositionSummary,
  setScrollPosition,
  setSortOrder: updateBlotterSortOrder,
  setColumnFilters: setBlotterColumnFilters,
  removeColumn: removeBlotterColumn,
  moveColumn: moveBlotterColumn,
});

export default connect(mapStateToProps, mapDispatchToProps)(Component);
