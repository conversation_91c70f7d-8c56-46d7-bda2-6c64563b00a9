import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { prop } from 'ramda';
import { isNotNil } from 'lib/utils';
import ConfirmationDialogView from 'lib/components/ConfirmationDialogView';

const BookNowConfirmationDialog = ({
  position,
  cancel,
  confirm,
  t,
}) => (
  <ConfirmationDialogView
    id="modal.agggregatedPosition.bookNowConfirmation"
    title={t('fatFinger.title.aggregatedPosition.bookNow')}
    subTitle={t('fatFinger.aggregatedPosition.bookNow.confirmation', {
      ...position,
      operationDisplayName: t(`OPERATIONS.${prop('operation', position)}`),
    })}
    isOpen={isNotNil(position)}
    onConfirm={() => confirm(position)}
    onCancel={cancel}
  />
);

BookNowConfirmationDialog.defaultProps = {
  position: null,
};

BookNowConfirmationDialog.propTypes = {
  confirm: PropTypes.func.isRequired,
  cancel: PropTypes.func.isRequired,
  position: PropTypes.object,
  t: PropTypes.func.isRequired,
};

export default withTranslation()(BookNowConfirmationDialog);
